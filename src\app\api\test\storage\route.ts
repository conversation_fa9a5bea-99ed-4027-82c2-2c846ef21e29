// 存储服务测试 API 端点
// 用于验证 Cloudflare R2 配置

import { NextRequest, NextResponse } from 'next/server';
import { storageProvider } from '@/lib/services/storage-service';

export async function GET(request: NextRequest) {
  try {
    // 检查环境变量
    const requiredEnvVars = [
      'CLOUDFLARE_R2_ENDPOINT',
      'CLOUDFLARE_R2_ACCESS_KEY_ID',
      'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
      'CLOUDFLARE_R2_BUCKET_NAME',
      'CLOUDFLARE_R2_PUBLIC_URL',
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Missing environment variables',
        missingVars,
      }, { status: 500 });
    }

    // 测试基本连接
    const testKey = `test/connection-test-${Date.now()}.txt`;
    const testContent = 'R2 connection test';
    const testFile = Buffer.from(testContent, 'utf-8');

    // 测试上传
    const uploadResult = await storageProvider.upload(testKey, testFile, {
      contentType: 'text/plain',
      metadata: {
        test: 'true',
        timestamp: new Date().toISOString(),
      },
    });

    if (uploadResult.error) {
      return NextResponse.json({
        success: false,
        error: 'Upload failed',
        details: uploadResult.error.message,
      }, { status: 500 });
    }

    // 测试下载
    const downloadResult = await storageProvider.download(testKey);
    if (downloadResult.error) {
      return NextResponse.json({
        success: false,
        error: 'Download failed',
        details: downloadResult.error.message,
      }, { status: 500 });
    }

    // 验证内容
    let downloadedContent = '';
    if (downloadResult.data) {
      downloadedContent = await downloadResult.data.text();
    }

    // 测试文件信息
    const infoResult = await storageProvider.getFileInfo(testKey);
    
    // 测试签名URL
    const signedUrlResult = await storageProvider.getSignedUrl(testKey, 300); // 5分钟

    // 清理测试文件
    await storageProvider.delete(testKey);

    return NextResponse.json({
      success: true,
      message: 'R2 storage configuration is working correctly',
      tests: {
        upload: {
          success: !uploadResult.error,
          url: uploadResult.url,
        },
        download: {
          success: !downloadResult.error,
          contentMatch: downloadedContent === testContent,
        },
        fileInfo: {
          success: !infoResult.error,
          size: infoResult.file?.size,
          contentType: infoResult.file?.contentType,
        },
        signedUrl: {
          success: !signedUrlResult.error,
          hasSignature: signedUrlResult.url.includes('X-Amz-Signature'),
        },
      },
      config: {
        endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
        bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
        publicUrl: process.env.CLOUDFLARE_R2_PUBLIC_URL,
      },
    });

  } catch (error) {
    console.error('Storage test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during storage test',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided',
      }, { status: 400 });
    }

    // 测试文件上传
    const testKey = `test/upload-test-${Date.now()}-${file.name}`;
    
    const uploadResult = await storageProvider.upload(testKey, file, {
      contentType: file.type,
      metadata: {
        originalName: file.name,
        uploadTest: 'true',
        timestamp: new Date().toISOString(),
      },
    });

    if (uploadResult.error) {
      return NextResponse.json({
        success: false,
        error: 'File upload failed',
        details: uploadResult.error.message,
      }, { status: 500 });
    }

    // 获取文件信息
    const infoResult = await storageProvider.getFileInfo(testKey);
    
    // 生成签名URL用于下载
    const signedUrlResult = await storageProvider.getSignedUrl(testKey, 3600);

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        key: testKey,
        url: uploadResult.url,
        signedUrl: signedUrlResult.url,
        info: infoResult.file,
      },
    });

  } catch (error) {
    console.error('File upload test error:', error);
    return NextResponse.json({
      success: false,
      error: 'File upload test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
