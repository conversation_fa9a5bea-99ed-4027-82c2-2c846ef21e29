// 存储服务配置
// 使用 Cloudflare R2 作为默认存储提供商

import { S3Client } from '@aws-sdk/client-s3';
import { CloudflareR2Provider, type StorageProvider } from '@/lib/abstractions/storage-provider';

// 创建 R2 客户端
const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
});

// 创建存储提供商实例
export const storageProvider: StorageProvider = new CloudflareR2Provider(
  r2Client,
  process.env.CLOUDFLARE_R2_BUCKET_NAME!,
  process.env.CLOUDFLARE_R2_PUBLIC_URL!
);

// 存储服务类
export class StorageService {
  constructor(private provider: StorageProvider) {}

  // 上传书签文件
  async uploadBookmarkFile(userId: string, file: File): Promise<{ key: string; url: string; error?: Error }> {
    const timestamp = Date.now();
    const key = `bookmarks/${userId}/${timestamp}-${file.name}`;
    
    const { url, error } = await this.provider.upload(key, file, {
      contentType: file.type,
      metadata: {
        userId,
        originalName: file.name,
        uploadedAt: new Date().toISOString(),
      },
    });

    if (error) {
      return { key: '', url: '', error };
    }

    return { key, url };
  }

  // 上传处理结果文件
  async uploadResultFile(userId: string, content: string, filename: string): Promise<{ key: string; url: string; error?: Error }> {
    const timestamp = Date.now();
    const key = `results/${userId}/${timestamp}-${filename}`;
    
    const buffer = Buffer.from(content, 'utf-8');
    
    const { url, error } = await this.provider.upload(key, buffer, {
      contentType: 'text/html',
      metadata: {
        userId,
        type: 'result',
        generatedAt: new Date().toISOString(),
      },
    });

    if (error) {
      return { key: '', url: '', error };
    }

    return { key, url };
  }

  // 下载文件
  async downloadFile(key: string): Promise<{ data: Blob | null; error?: Error }> {
    return this.provider.download(key);
  }

  // 获取文件信息
  async getFileInfo(key: string) {
    return this.provider.getFileInfo(key);
  }

  // 获取公开访问URL
  getPublicUrl(key: string): string {
    return this.provider.getPublicUrl(key);
  }

  // 获取签名URL（临时访问）
  async getSignedUrl(key: string, expiresIn = 3600) {
    return this.provider.getSignedUrl(key, expiresIn);
  }

  // 删除文件
  async deleteFile(key: string) {
    return this.provider.delete(key);
  }

  // 批量删除文件
  async deleteFiles(keys: string[]) {
    return this.provider.deleteMany(keys);
  }

  // 清理用户的旧文件
  async cleanupUserFiles(userId: string, keepRecent = 5): Promise<{ deletedCount: number; error?: Error }> {
    try {
      // 列出用户的所有文件
      const { files, error } = await this.provider.list(`bookmarks/${userId}/`);
      
      if (error) {
        return { deletedCount: 0, error };
      }

      // 按时间排序，保留最近的文件
      const sortedFiles = files.sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime());
      const filesToDelete = sortedFiles.slice(keepRecent);

      if (filesToDelete.length === 0) {
        return { deletedCount: 0 };
      }

      // 批量删除旧文件
      const keys = filesToDelete.map(file => file.key);
      const { errors } = await this.provider.deleteMany(keys);
      
      const deletedCount = errors.filter(error => error === null).length;
      const hasErrors = errors.some(error => error !== null);

      return {
        deletedCount,
        error: hasErrors ? new Error('Some files failed to delete') : undefined,
      };
    } catch (error) {
      return {
        deletedCount: 0,
        error: error as Error,
      };
    }
  }
}

// 导出单例实例
export const storageService = new StorageService(storageProvider);
