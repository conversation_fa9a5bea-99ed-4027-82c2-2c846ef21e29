// 数据访问服务层
// 使用 Prisma ORM 进行数据库操作

import { prisma } from '@/lib/prisma/client';
import type { 
  Profile, 
  UserBookmarkWorkspace, 
  UserCategory, 
  BookmarkProcessingCache 
} from '@prisma/client';

export class DataService {
  // 用户相关操作
  async createProfile(data: {
    id: string;
    email: string;
    displayName?: string;
  }): Promise<Profile> {
    return prisma.profile.create({
      data,
    });
  }

  // 根据邮箱查找用户（用于账号关联）
  async findProfileByEmail(email: string): Promise<Profile | null> {
    return prisma.profile.findFirst({
      where: { email: email.toLowerCase() },
    });
  }

  // 账号关联：将新的认证方式关联到现有账户
  async linkAccountToProfile(profileId: string, newAuthId: string): Promise<boolean> {
    try {
      // 这里需要根据实际的认证系统实现
      // 由于 Supabase 的限制，我们可能需要在应用层处理账号关联
      // 暂时返回 true，实际实现需要根据具体需求调整
      return true;
    } catch (error) {
      console.error('Account linking failed:', error);
      return false;
    }
  }

  async getProfile(userId: string): Promise<Profile | null> {
    return prisma.profile.findUnique({
      where: { id: userId },
    });
  }

  async updateProfile(userId: string, data: {
    displayName?: string;
  }): Promise<Profile> {
    return prisma.profile.update({
      where: { id: userId },
      data,
    });
  }

  // 工作空间相关操作
  async createWorkspace(userId: string): Promise<UserBookmarkWorkspace> {
    return prisma.userBookmarkWorkspace.create({
      data: {
        userId,
      },
    });
  }

  async getWorkspace(userId: string): Promise<UserBookmarkWorkspace | null> {
    return prisma.userBookmarkWorkspace.findUnique({
      where: { userId },
    });
  }

  async updateWorkspace(
    userId: string,
    data: {
      status?: string;
      originalFileKey?: string | null;
      resultFileKey?: string | null;
      originalFilename?: string | null;
      browserType?: string | null;
      totalBookmarks?: number;
      processedBookmarks?: number;
      aiLearningExamples?: any;
      processingMetadata?: any;
      lastImportAt?: Date | null;
    }
  ): Promise<UserBookmarkWorkspace> {
    return prisma.userBookmarkWorkspace.update({
      where: { userId },
      data,
    });
  }

  async getWorkspaceWithBookmarks(userId: string) {
    return prisma.userBookmarkWorkspace.findUnique({
      where: { userId },
      include: {
        bookmarks: true,
      },
    });
  }

  // 分类相关操作
  async createCategory(data: {
    userId: string;
    name: string;
    type?: string;
    color?: string;
    description?: string;
    sortOrder?: number;
    isDefault?: boolean;
  }): Promise<UserCategory> {
    return prisma.userCategory.create({
      data,
    });
  }

  async getCategories(userId: string): Promise<UserCategory[]> {
    return prisma.userCategory.findMany({
      where: { userId },
      orderBy: { sortOrder: 'asc' },
    });
  }

  async updateCategory(
    categoryId: string,
    data: {
      name?: string;
      type?: string;
      color?: string | null;
      description?: string | null;
      sortOrder?: number;
      isDefault?: boolean;
    }
  ): Promise<UserCategory> {
    return prisma.userCategory.update({
      where: { id: categoryId },
      data,
    });
  }

  async deleteCategory(categoryId: string): Promise<void> {
    await prisma.userCategory.delete({
      where: { id: categoryId },
    });
  }

  async createDefaultCategories(userId: string): Promise<UserCategory[]> {
    const defaultCategories = [
      { name: '未分类', type: 'manual', color: '#6b7280', isDefault: true, sortOrder: 0 },
      { name: '收件箱', type: 'manual', color: '#3b82f6', isDefault: true, sortOrder: 1 },
    ];

    const categories = await Promise.all(
      defaultCategories.map(category =>
        this.createCategory({ userId, ...category })
      )
    );

    return categories;
  }

  // 书签缓存相关操作
  async createBookmark(data: {
    workspaceId: string;
    title: string;
    url: string;
    originalPath?: string;
    status?: string;
    aiSuggestion?: any;
    suggestedCategory?: string;
    finalCategory?: string;
    classificationType?: string;
    isDuplicate?: boolean;
    duplicateUrl?: string;
  }): Promise<BookmarkProcessingCache> {
    return prisma.bookmarkProcessingCache.create({
      data,
    });
  }

  async batchCreateBookmarks(
    workspaceId: string,
    bookmarks: Array<{
      title: string;
      url: string;
      originalPath?: string;
    }>
  ): Promise<number> {
    const result = await prisma.bookmarkProcessingCache.createMany({
      data: bookmarks.map(bookmark => ({
        workspaceId,
        ...bookmark,
      })),
    });

    return result.count;
  }

  async getBookmarks(workspaceId: string): Promise<BookmarkProcessingCache[]> {
    return prisma.bookmarkProcessingCache.findMany({
      where: { workspaceId },
      orderBy: { createdAt: 'asc' },
    });
  }

  async updateBookmark(
    bookmarkId: string,
    data: {
      title?: string;
      url?: string;
      originalPath?: string | null;
      status?: string;
      aiSuggestion?: any;
      suggestedCategory?: string | null;
      finalCategory?: string | null;
      classificationType?: string;
      isDuplicate?: boolean;
      duplicateUrl?: string | null;
    }
  ): Promise<BookmarkProcessingCache> {
    return prisma.bookmarkProcessingCache.update({
      where: { id: bookmarkId },
      data,
    });
  }

  async batchUpdateBookmarks(
    updates: Array<{
      id: string;
      finalCategory?: string;
      status?: string;
    }>
  ): Promise<void> {
    await Promise.all(
      updates.map(update => {
        const data: any = {};
        if (update.finalCategory !== undefined) {
          data.finalCategory = update.finalCategory;
        }
        if (update.status !== undefined) {
          data.status = update.status;
        }
        return prisma.bookmarkProcessingCache.update({
          where: { id: update.id },
          data,
        });
      })
    );
  }

  async clearBookmarkCache(workspaceId: string): Promise<void> {
    await prisma.bookmarkProcessingCache.deleteMany({
      where: { workspaceId },
    });
  }

  async getBookmarkStats(workspaceId: string) {
    const stats = await prisma.bookmarkProcessingCache.groupBy({
      by: ['status'],
      where: { workspaceId },
      _count: true,
    });

    const duplicateCount = await prisma.bookmarkProcessingCache.count({
      where: { workspaceId, isDuplicate: true },
    });

    return {
      total: stats.reduce((sum, stat) => sum + stat._count, 0),
      unprocessed: stats.find(s => s.status === 'unprocessed')?._count || 0,
      confirmed: stats.find(s => s.status === 'confirmed')?._count || 0,
      rejected: stats.find(s => s.status === 'rejected')?._count || 0,
      duplicates: duplicateCount,
    };
  }

  async detectDuplicateBookmarks(workspaceId: string): Promise<number> {
    // 查找重复的 URL
    const duplicates = await prisma.bookmarkProcessingCache.groupBy({
      by: ['url'],
      where: { workspaceId },
      having: {
        url: {
          _count: {
            gt: 1,
          },
        },
      },
      _min: {
        createdAt: true,
      },
    });

    let updatedCount = 0;

    // 标记重复的书签（保留最早的）
    for (const duplicate of duplicates) {
      const result = await prisma.bookmarkProcessingCache.updateMany({
        where: {
          workspaceId,
          url: duplicate.url,
          createdAt: {
            gt: duplicate._min.createdAt!,
          },
        },
        data: {
          isDuplicate: true,
          duplicateUrl: duplicate.url,
        },
      });

      updatedCount += result.count;
    }

    return updatedCount;
  }

  // 事务操作
  async initializeUserData(userId: string, email: string, displayName?: string) {
    return prisma.$transaction(async (tx) => {
      // 创建用户档案
      const profile = await tx.profile.create({
        data: {
          id: userId,
          email,
          displayName: displayName || null,
        },
      });

      // 创建工作空间
      const workspace = await tx.userBookmarkWorkspace.create({
        data: {
          userId,
        },
      });

      // 创建默认分类
      const categories = await tx.userCategory.createMany({
        data: [
          { userId, name: '未分类', type: 'manual', color: '#6b7280', isDefault: true, sortOrder: 0 },
          { userId, name: '收件箱', type: 'manual', color: '#3b82f6', isDefault: true, sortOrder: 1 },
        ],
      });

      return { profile, workspace, categories };
    });
  }
}

// 导出单例实例
export const dataService = new DataService();
