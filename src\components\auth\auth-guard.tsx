'use client';

import React from 'react';
import { useAuth } from '@/lib/auth/auth-context';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean;
  roles?: string[];
}

/**
 * 认证守卫组件
 * 根据认证状态和权限控制内容显示
 */
export function AuthGuard({ 
  children, 
  fallback = null,
  requireAuth = true,
  roles = []
}: AuthGuardProps) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-sm text-gray-600">验证中...</span>
      </div>
    );
  }

  // 需要认证但用户未登录
  if (requireAuth && !user) {
    return <>{fallback}</>;
  }

  // 不需要认证但用户已登录（用于登录页面等）
  if (!requireAuth && user) {
    return <>{fallback}</>;
  }

  // 检查角色权限（如果需要）
  if (roles.length > 0 && user) {
    const userRoles = user.metadata?.roles || [];
    const hasRequiredRole = roles.some(role => userRoles.includes(role));
    
    if (!hasRequiredRole) {
      return (
        <div className="text-center p-4">
          <p className="text-red-600">您没有访问此内容的权限</p>
        </div>
      );
    }
  }

  return <>{children}</>;
}

/**
 * 仅认证用户可见组件
 */
export function AuthenticatedOnly({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode; 
}) {
  return (
    <AuthGuard requireAuth={true} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

/**
 * 仅未认证用户可见组件
 */
export function UnauthenticatedOnly({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode; 
}) {
  return (
    <AuthGuard requireAuth={false} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}
