// 存储服务测试
// 用于验证 Cloudflare R2 配置是否正确

import { storageProvider } from '../storage-service';

describe('Storage Service', () => {
  // 跳过实际的网络请求测试，除非在集成测试环境中
  const shouldRunIntegrationTests = process.env.RUN_INTEGRATION_TESTS === 'true';

  describe('Configuration', () => {
    it('should have storage provider configured', () => {
      expect(storageProvider).toBeDefined();
    });

    it('should have required environment variables', () => {
      // 在测试环境中，这些可能是模拟值
      if (shouldRunIntegrationTests) {
        expect(process.env.CLOUDFLARE_R2_ENDPOINT).toBeDefined();
        expect(process.env.CLOUDFLARE_R2_ACCESS_KEY_ID).toBeDefined();
        expect(process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY).toBeDefined();
        expect(process.env.CLOUDFLARE_R2_BUCKET_NAME).toBeDefined();
        expect(process.env.CLOUDFLARE_R2_PUBLIC_URL).toBeDefined();
      }
    });
  });

  describe('Storage Operations', () => {
    const testUserId = 'test-user-123';
    const testKey = `test/${testUserId}/test-file.txt`;

    // 只在集成测试环境中运行实际的存储操作
    if (shouldRunIntegrationTests) {
      it('should upload and download a file', async () => {
        const testContent = 'Hello, R2 Storage!';
        const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });

        // 上传文件
        const uploadResult = await storageProvider.upload(testKey, testFile, {
          contentType: 'text/plain',
          metadata: {
            userId: testUserId,
            test: 'true',
          },
        });

        expect(uploadResult.error).toBeUndefined();
        expect(uploadResult.url).toContain(testKey);

        // 下载文件
        const downloadResult = await storageProvider.download(testKey);
        expect(downloadResult.error).toBeUndefined();
        expect(downloadResult.data).toBeDefined();

        if (downloadResult.data) {
          const downloadedContent = await downloadResult.data.text();
          expect(downloadedContent).toBe(testContent);
        }

        // 清理：删除测试文件
        await storageProvider.delete(testKey);
      }, 10000); // 10秒超时

      it('should get file info', async () => {
        const testContent = 'File info test';
        const testFile = new File([testContent], 'info-test.txt', { type: 'text/plain' });

        // 上传文件
        await storageProvider.upload(testKey, testFile);

        // 获取文件信息
        const infoResult = await storageProvider.getFileInfo(testKey);
        expect(infoResult.error).toBeUndefined();
        expect(infoResult.file).toBeDefined();

        if (infoResult.file) {
          expect(infoResult.file.key).toBe(testKey);
          expect(infoResult.file.size).toBeGreaterThan(0);
          expect(infoResult.file.contentType).toBe('text/plain');
        }

        // 清理
        await storageProvider.delete(testKey);
      }, 10000);

      it('should generate signed URL', async () => {
        const testContent = 'Signed URL test';
        const testFile = new File([testContent], 'signed-test.txt', { type: 'text/plain' });

        // 上传文件
        await storageProvider.upload(testKey, testFile);

        // 生成签名URL
        const signedUrlResult = await storageProvider.getSignedUrl(testKey, 3600);
        expect(signedUrlResult.error).toBeUndefined();
        expect(signedUrlResult.url).toContain('X-Amz-Signature');

        // 清理
        await storageProvider.delete(testKey);
      }, 10000);

      it('should list files', async () => {
        const testPrefix = `test/${testUserId}/list-test`;
        const testFiles = ['file1.txt', 'file2.txt', 'file3.txt'];

        // 上传多个测试文件
        for (const fileName of testFiles) {
          const content = `Content of ${fileName}`;
          const file = new File([content], fileName, { type: 'text/plain' });
          await storageProvider.upload(`${testPrefix}/${fileName}`, file);
        }

        // 列出文件
        const listResult = await storageProvider.list(testPrefix);
        expect(listResult.error).toBeUndefined();
        expect(listResult.files).toHaveLength(testFiles.length);

        // 清理
        for (const fileName of testFiles) {
          await storageProvider.delete(`${testPrefix}/${fileName}`);
        }
      }, 15000);
    } else {
      it('should skip integration tests when not enabled', () => {
        console.log('Integration tests skipped. Set RUN_INTEGRATION_TESTS=true to run them.');
        expect(true).toBe(true);
      });
    }
  });

  describe('Error Handling', () => {
    it('should handle non-existent file download', async () => {
      const result = await storageProvider.download('non-existent-file.txt');
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
    });

    it('should handle non-existent file info', async () => {
      const result = await storageProvider.getFileInfo('non-existent-file.txt');
      expect(result.file).toBeNull();
      expect(result.error).toBeDefined();
    });
  });
});
