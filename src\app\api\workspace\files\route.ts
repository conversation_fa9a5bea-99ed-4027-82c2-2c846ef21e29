// 文件管理 API 端点
// 处理文件的列表、删除等操作

import { NextRequest, NextResponse } from 'next/server';
import { storageProvider } from '@/lib/services/storage-service';
import { createClient } from '@/lib/supabase/server';

// 获取用户的文件列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');
    const fileType = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '50');

    // 构建文件前缀
    let prefix = '';
    if (workspaceId) {
      prefix = `workspaces/${workspaceId}/${user.id}/`;
    } else {
      prefix = `uploads/${user.id}/`;
    }

    // 获取文件列表
    const listResult = await storageProvider.list(prefix, limit);

    if (listResult.error) {
      return NextResponse.json({
        success: false,
        error: 'Failed to list files',
        details: listResult.error.message,
      }, { status: 500 });
    }

    // 过滤文件类型（如果指定）
    let files = listResult.files;
    if (fileType) {
      files = files.filter(file => {
        // 从文件名或元数据中判断文件类型
        return file.key.includes(`-${fileType}-`) || file.contentType?.startsWith(fileType);
      });
    }

    // 为每个文件生成签名URL
    const filesWithSignedUrls = await Promise.all(
      files.map(async (file) => {
        const signedUrlResult = await storageProvider.getSignedUrl(file.key, 3600);
        return {
          ...file,
          signedUrl: signedUrlResult.url,
        };
      })
    );

    return NextResponse.json({
      success: true,
      files: filesWithSignedUrls,
      total: files.length,
      prefix,
    });

  } catch (error) {
    console.error('Files list API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// 删除文件
export async function DELETE(request: NextRequest) {
  try {
    // 验证用户身份
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // 获取请求体
    const body = await request.json();
    const { fileKey } = body;

    if (!fileKey) {
      return NextResponse.json({
        success: false,
        error: 'File key is required',
      }, { status: 400 });
    }

    // 验证文件所有权（确保用户只能删除自己的文件）
    if (!fileKey.includes(user.id)) {
      return NextResponse.json({
        success: false,
        error: 'Access denied',
      }, { status: 403 });
    }

    // 删除文件
    const deleteResult = await storageProvider.delete(fileKey);

    if (deleteResult.error) {
      return NextResponse.json({
        success: false,
        error: 'Failed to delete file',
        details: deleteResult.error.message,
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully',
      fileKey,
    });

  } catch (error) {
    console.error('File delete API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
