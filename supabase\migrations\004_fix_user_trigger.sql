-- 修复用户注册触发器
-- 解决 Google OAuth 注册时的数据库错误

-- 删除现有触发器
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 重新创建改进的用户处理函数
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- 插入用户资料，使用 ON CONFLICT 避免重复插入
    INSERT INTO profiles (id, email, display_name)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(
            NEW.raw_user_meta_data->>'display_name', 
            NEW.raw_user_meta_data->>'full_name', 
            split_part(NEW.email, '@', 1)
        )
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        display_name = COALESCE(EXCLUDED.display_name, profiles.display_name),
        updated_at = NOW();
    
    -- 插入用户工作空间，使用 ON CONFLICT 避免重复插入
    INSERT INTO user_bookmark_workspace (user_id)
    VALUES (NEW.id)
    ON CONFLICT (user_id) DO NOTHING;
    
    -- 创建默认分类，使用 ON CONFLICT 避免重复插入
    INSERT INTO user_categories (user_id, name, type, color, is_default, sort_order)
    VALUES 
        (NEW.id, '未分类', 'manual', '#6b7280', true, 0),
        (NEW.id, '收件箱', 'manual', '#3b82f6', true, 1)
    ON CONFLICT (user_id, name) DO NOTHING;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- 记录错误但不阻止用户创建
        RAISE WARNING 'Error in handle_new_user for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 重新创建触发器
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
