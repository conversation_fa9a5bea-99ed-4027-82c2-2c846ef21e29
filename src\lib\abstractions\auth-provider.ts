// 认证抽象层接口
// 支持不同的认证服务提供商

import { User, Session, AuthError } from '@supabase/supabase-js';

export interface AuthUser {
  id: string;
  email: string;
  displayName?: string;
  metadata?: Record<string, any>;
}

export interface AuthSession {
  user: AuthUser;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number | undefined;
}

export interface AuthProvider {
  // 基础认证方法
  signIn(email: string, password: string): Promise<{ user: AuthUser | null; error: AuthError | null }>;
  signUp(email: string, password: string): Promise<{ user: AuthUser | null; error: AuthError | null }>;
  signOut(): Promise<{ error: AuthError | null }>;
  
  // OAuth 方法
  signInWithGoogle(): Promise<{ error: AuthError | null }>;
  
  // 密码管理
  resetPassword(email: string): Promise<{ error: AuthError | null }>;
  updatePassword(password: string): Promise<{ error: AuthError | null }>;
  
  // 会话管理
  getSession(): Promise<{ session: AuthSession | null; error: AuthError | null }>;
  refreshSession(): Promise<{ session: AuthSession | null; error: AuthError | null }>;
  
  // 事件监听
  onAuthStateChange(callback: (event: string, session: AuthSession | null) => void): () => void;
}

// Supabase Auth 适配器
export class SupabaseAuthProvider implements AuthProvider {
  constructor(private supabaseClient: any) {}

  private mapUser(supabaseUser: User | null): AuthUser | null {
    if (!supabaseUser) return null;
    
    return {
      id: supabaseUser.id,
      email: supabaseUser.email!,
      displayName: supabaseUser.user_metadata?.display_name,
      metadata: supabaseUser.user_metadata,
    };
  }

  private mapSession(supabaseSession: Session | null): AuthSession | null {
    if (!supabaseSession) return null;
    
    return {
      user: this.mapUser(supabaseSession.user)!,
      accessToken: supabaseSession.access_token,
      refreshToken: supabaseSession.refresh_token,
      expiresAt: supabaseSession.expires_at,
    };
  }

  async signIn(email: string, password: string) {
    const { data, error } = await this.supabaseClient.auth.signInWithPassword({
      email,
      password,
    });

    return {
      user: this.mapUser(data.user),
      error,
    };
  }

  async signUp(email: string, password: string) {
    const { data, error } = await this.supabaseClient.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    return {
      user: this.mapUser(data.user),
      error,
    };
  }

  async signOut() {
    const { error } = await this.supabaseClient.auth.signOut();
    return { error };
  }

  async signInWithGoogle() {
    const { error } = await this.supabaseClient.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    return { error };
  }

  async resetPassword(email: string) {
    const { error } = await this.supabaseClient.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    return { error };
  }

  async updatePassword(password: string) {
    const { error } = await this.supabaseClient.auth.updateUser({
      password,
    });

    return { error };
  }

  async getSession() {
    const { data, error } = await this.supabaseClient.auth.getSession();
    
    return {
      session: this.mapSession(data.session),
      error,
    };
  }

  async refreshSession() {
    const { data, error } = await this.supabaseClient.auth.refreshSession();
    
    return {
      session: this.mapSession(data.session),
      error,
    };
  }

  onAuthStateChange(callback: (event: string, session: AuthSession | null) => void) {
    const { data: { subscription } } = this.supabaseClient.auth.onAuthStateChange(
      (event: string, supabaseSession: Session | null) => {
        callback(event, this.mapSession(supabaseSession));
      }
    );

    return () => subscription.unsubscribe();
  }
}
