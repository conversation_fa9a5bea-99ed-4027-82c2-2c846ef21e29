'use client';

// 书签解析测试页面
// 用于测试书签文件解析功能

import { useState } from 'react';
import { useAuth } from '@/lib/auth/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Upload, FileText, Folder, Link, AlertCircle, CheckCircle } from 'lucide-react';

interface ParseResult {
  success: boolean;
  data?: {
    totalBookmarks: number;
    totalFolders: number;
    browserType: string;
    exportDate?: string;
    bookmarks: any[];
    folders: any[];
    warnings: string[];
  };
  error?: string;
  details?: string[];
}

export default function ParseTestPage() {
  const { user, isLoading: authLoading } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [workspaceId, setWorkspaceId] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [isParsing, setIsParsing] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [parseResult, setParseResult] = useState<ParseResult | null>(null);

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadResult(null);
      setParseResult(null);
    }
  };

  // 处理文件上传
  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('type', 'document');
      if (workspaceId) formData.append('workspaceId', workspaceId);

      const response = await fetch('/api/workspace/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      setUploadResult(result);

    } catch (error) {
      setUploadResult({
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // 处理书签解析
  const handleParse = async () => {
    if (!uploadResult?.success || !uploadResult.file?.key) return;

    setIsParsing(true);
    setParseResult(null);

    try {
      const response = await fetch('/api/workspace/parse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileKey: uploadResult.file.key,
          workspaceId: workspaceId || undefined,
        }),
      });

      const result = await response.json();
      setParseResult(result);

    } catch (error) {
      setParseResult({
        success: false,
        error: error instanceof Error ? error.message : 'Parse failed',
      });
    } finally {
      setIsParsing(false);
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <p className="text-center text-gray-600">请先登录以使用书签解析功能</p>
            <Button className="w-full mt-4" onClick={() => window.location.href = '/auth/login'}>
              前往登录
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">书签解析测试</h1>
          <p className="text-gray-600">测试 HTML 书签文件解析功能</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 上传和解析表单 */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  文件上传
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="file">选择书签文件 (HTML)</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".html,.htm"
                    onChange={handleFileSelect}
                    className="mt-1"
                  />
                  {selectedFile && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg flex items-center gap-3">
                      <FileText className="w-5 h-5 text-blue-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{selectedFile.name}</p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(selectedFile.size)} • {selectedFile.type}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="workspaceId">工作区 ID (可选)</Label>
                  <Input
                    id="workspaceId"
                    value={workspaceId}
                    onChange={(e) => setWorkspaceId(e.target.value)}
                    placeholder="输入工作区 ID"
                  />
                </div>

                <Button
                  onClick={handleUpload}
                  disabled={!selectedFile || isUploading}
                  className="w-full"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      上传中...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      上传文件
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {uploadResult?.success && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    解析书签
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={handleParse}
                    disabled={isParsing}
                    className="w-full"
                  >
                    {isParsing ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        解析中...
                      </>
                    ) : (
                      <>
                        <FileText className="w-4 h-4 mr-2" />
                        解析书签文件
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 结果显示 */}
          <div className="space-y-6">
            {/* 上传结果 */}
            {uploadResult && (
              <Card>
                <CardHeader>
                  <CardTitle>上传结果</CardTitle>
                </CardHeader>
                <CardContent>
                  {uploadResult.success ? (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <h3 className="font-medium text-green-800">上传成功！</h3>
                      </div>
                      <div className="space-y-1 text-sm text-green-700">
                        <p><strong>文件名:</strong> {uploadResult.file.name}</p>
                        <p><strong>存储路径:</strong> {uploadResult.file.key}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertCircle className="w-5 h-5 text-red-500" />
                        <h3 className="font-medium text-red-800">上传失败</h3>
                      </div>
                      <p className="text-sm text-red-600">{uploadResult.error}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* 解析结果 */}
            {parseResult && (
              <Card>
                <CardHeader>
                  <CardTitle>解析结果</CardTitle>
                </CardHeader>
                <CardContent>
                  {parseResult.success ? (
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-3">
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <h3 className="font-medium text-green-800">解析成功！</h3>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Link className="w-4 h-4 text-blue-500" />
                            <span><strong>{parseResult.data?.totalBookmarks}</strong> 个书签</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Folder className="w-4 h-4 text-yellow-500" />
                            <span><strong>{parseResult.data?.totalFolders}</strong> 个文件夹</span>
                          </div>
                        </div>

                        <div className="mt-3 text-sm text-green-700">
                          <p><strong>浏览器类型:</strong> {parseResult.data?.browserType}</p>
                          {parseResult.data?.exportDate && (
                            <p><strong>导出日期:</strong> {new Date(parseResult.data.exportDate).toLocaleDateString()}</p>
                          )}
                        </div>

                        {parseResult.data?.warnings && parseResult.data.warnings.length > 0 && (
                          <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                            <p className="text-sm font-medium text-yellow-800">警告:</p>
                            <ul className="text-sm text-yellow-700 list-disc list-inside">
                              {parseResult.data.warnings.map((warning, index) => (
                                <li key={index}>{warning}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      {/* 书签预览 */}
                      {parseResult.data?.bookmarks && parseResult.data.bookmarks.length > 0 && (
                        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <h4 className="font-medium text-blue-800 mb-2">书签预览 (前5个)</h4>
                          <div className="space-y-2">
                            {parseResult.data.bookmarks.slice(0, 5).map((bookmark, index) => (
                              <div key={index} className="text-sm">
                                <p className="font-medium text-blue-900">{bookmark.title}</p>
                                <p className="text-blue-600 truncate">{bookmark.url}</p>
                                {bookmark.folderPath && (
                                  <p className="text-blue-500 text-xs">📁 {bookmark.folderPath.join(' > ')}</p>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertCircle className="w-5 h-5 text-red-500" />
                        <h3 className="font-medium text-red-800">解析失败</h3>
                      </div>
                      <p className="text-sm text-red-600">{parseResult.error}</p>
                      {parseResult.details && (
                        <ul className="mt-2 text-sm text-red-600 list-disc list-inside">
                          {parseResult.details.map((detail, index) => (
                            <li key={index}>{detail}</li>
                          ))}
                        </ul>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
