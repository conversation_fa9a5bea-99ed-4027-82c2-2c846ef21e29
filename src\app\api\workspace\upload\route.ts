// 文件上传 API 端点
// 处理书签相关文件的上传，包括图标、截图等

import { NextRequest, NextResponse } from 'next/server';
import { storageProvider } from '@/lib/services/storage-service';
import { createClient } from '@/lib/supabase/server';

// 支持的文件类型
const ALLOWED_FILE_TYPES = {
  image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  document: ['application/pdf', 'text/html', 'text/plain'],
  archive: ['application/zip', 'application/x-rar-compressed'],
} as const;

// 文件大小限制 (字节)
const MAX_FILE_SIZES = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  archive: 50 * 1024 * 1024, // 50MB
} as const;

// 获取文件类型分类
function getFileCategory(mimeType: string): keyof typeof ALLOWED_FILE_TYPES | null {
  for (const [category, types] of Object.entries(ALLOWED_FILE_TYPES)) {
    if (types.includes(mimeType as any)) {
      return category as keyof typeof ALLOWED_FILE_TYPES;
    }
  }
  return null;
}

// 生成安全的文件名
function generateSafeFileName(originalName: string, userId: string): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop() || '';
  const baseName = originalName.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9-_]/g, '_');
  
  return `${userId}/${timestamp}-${randomSuffix}-${baseName}.${extension}`;
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // 解析表单数据
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const fileType = formData.get('type') as string; // 'icon', 'screenshot', 'document'
    const workspaceId = formData.get('workspaceId') as string;

    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided',
      }, { status: 400 });
    }

    // 验证文件类型
    const fileCategory = getFileCategory(file.type);
    if (!fileCategory) {
      return NextResponse.json({
        success: false,
        error: 'Unsupported file type',
        supportedTypes: Object.values(ALLOWED_FILE_TYPES).flat(),
      }, { status: 400 });
    }

    // 验证文件大小
    const maxSize = MAX_FILE_SIZES[fileCategory];
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        error: 'File too large',
        maxSize: maxSize,
        actualSize: file.size,
      }, { status: 400 });
    }

    // 生成文件路径
    const fileKey = generateSafeFileName(file.name, user.id);
    const fullPath = workspaceId ? `workspaces/${workspaceId}/${fileKey}` : `uploads/${fileKey}`;

    // 上传文件到 R2
    const uploadResult = await storageProvider.upload(fullPath, file, {
      contentType: file.type,
      metadata: {
        userId: user.id,
        workspaceId: workspaceId || '',
        fileType: fileType || 'unknown',
        originalName: file.name,
        uploadedAt: new Date().toISOString(),
      },
      cacheControl: 'public, max-age=31536000', // 1年缓存
    });

    if (uploadResult.error) {
      console.error('File upload error:', uploadResult.error);
      return NextResponse.json({
        success: false,
        error: 'Upload failed',
        details: uploadResult.error.message,
      }, { status: 500 });
    }

    // 获取文件信息
    const fileInfo = await storageProvider.getFileInfo(fullPath);
    
    // 生成签名URL（用于安全访问）
    const signedUrlResult = await storageProvider.getSignedUrl(fullPath, 3600); // 1小时有效

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        key: fullPath,
        url: uploadResult.url,
        signedUrl: signedUrlResult.url,
        name: file.name,
        size: file.size,
        type: file.type,
        category: fileCategory,
        info: fileInfo.file,
        metadata: {
          userId: user.id,
          workspaceId: workspaceId || null,
          fileType: fileType || 'unknown',
          uploadedAt: new Date().toISOString(),
        },
      },
    });

  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// 获取上传配置信息
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    return NextResponse.json({
      success: true,
      config: {
        allowedTypes: ALLOWED_FILE_TYPES,
        maxSizes: MAX_FILE_SIZES,
        supportedCategories: Object.keys(ALLOWED_FILE_TYPES),
      },
    });

  } catch (error) {
    console.error('Upload config API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
