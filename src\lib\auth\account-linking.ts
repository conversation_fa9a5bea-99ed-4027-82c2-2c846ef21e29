// 账号关联处理逻辑
// 处理邮箱登录和 Google 登录的账号关联

import { dataService } from '@/lib/services/data-service';
import type { AuthUser } from '@/lib/abstractions/auth-provider';

export interface AccountLinkingResult {
  success: boolean;
  isNewUser: boolean;
  linkedAccount?: boolean;
  profile?: any;
  error?: string;
}

/**
 * 处理用户登录后的账号关联逻辑
 * @param user 认证用户信息
 * @param authMethod 认证方式 ('email' | 'google')
 */
export async function handleAccountLinking(
  user: AuthUser,
  authMethod: 'email' | 'google'
): Promise<AccountLinkingResult> {
  try {
    const email = user.email.toLowerCase();
    
    // 查找是否已存在相同邮箱的用户
    const existingProfile = await dataService.findProfileByEmail(email);
    
    if (existingProfile) {
      // 用户已存在，检查是否是同一个用户ID
      if (existingProfile.id === user.id) {
        // 同一个用户，正常登录
        return {
          success: true,
          isNewUser: false,
          profile: existingProfile,
        };
      } else {
        // 不同的用户ID但相同邮箱，需要账号关联
        console.log(`Account linking detected: ${authMethod} login for existing email ${email}`);
        
        // 在实际应用中，这里可能需要：
        // 1. 提示用户确认账号关联
        // 2. 合并用户数据
        // 3. 更新认证记录
        
        // 由于 Supabase 的限制，我们采用以下策略：
        // - 如果是 Google 登录，提示用户使用邮箱登录
        // - 如果是邮箱登录，提示用户该邮箱已被 Google 账户使用
        
        return {
          success: false,
          isNewUser: false,
          error: authMethod === 'google' 
            ? `该邮箱 ${email} 已注册，请使用邮箱密码登录，或在登录后关联 Google 账户`
            : `该邮箱 ${email} 已与 Google 账户关联，请使用 Google 登录`
        };
      }
    } else {
      // 新用户，创建用户档案
      try {
        const profile = await dataService.createProfile({
          id: user.id,
          email: email,
          displayName: user.displayName,
        });

        // 创建默认工作空间和分类
        await dataService.createWorkspace(user.id);
        await dataService.createDefaultCategories(user.id);

        return {
          success: true,
          isNewUser: true,
          profile,
        };
      } catch (error) {
        console.error('Failed to create user profile:', error);
        return {
          success: false,
          isNewUser: true,
          error: '创建用户档案失败，请稍后重试',
        };
      }
    }
  } catch (error) {
    console.error('Account linking error:', error);
    return {
      success: false,
      isNewUser: false,
      error: '账号处理失败，请稍后重试',
    };
  }
}

/**
 * 手动关联账户（用户确认后）
 * 这个功能可以在用户设置页面中提供
 */
export async function manualAccountLinking(
  primaryUserId: string,
  secondaryUserId: string,
  email: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // 1. 获取两个账户的数据
    const primaryProfile = await dataService.getProfile(primaryUserId);
    const secondaryProfile = await dataService.getProfile(secondaryUserId);
    
    if (!primaryProfile || !secondaryProfile) {
      return { success: false, error: '找不到用户档案' };
    }

    // 2. 合并用户数据（保留主账户的数据，合并次要账户的数据）
    // 这里可以根据业务需求决定如何合并数据
    
    // 3. 删除次要账户的数据
    // 注意：这需要谨慎处理，确保数据不丢失
    
    // 4. 更新认证记录（这部分需要根据具体的认证系统实现）
    
    console.log(`Manual account linking: ${primaryUserId} <- ${secondaryUserId} for ${email}`);
    
    return { success: true };
  } catch (error) {
    console.error('Manual account linking error:', error);
    return { success: false, error: '账号关联失败' };
  }
}

/**
 * 检查邮箱是否已被其他认证方式使用
 */
export async function checkEmailConflict(
  email: string,
  currentUserId: string
): Promise<{
  hasConflict: boolean;
  conflictType?: 'email' | 'google';
  message?: string;
}> {
  try {
    const existingProfile = await dataService.findProfileByEmail(email.toLowerCase());
    
    if (!existingProfile || existingProfile.id === currentUserId) {
      return { hasConflict: false };
    }

    // 这里可以根据用户的认证方式来判断冲突类型
    // 由于我们没有存储认证方式的详细信息，暂时返回通用消息
    return {
      hasConflict: true,
      message: `该邮箱已被其他账户使用，如果这是您的账户，请联系客服进行账号关联`,
    };
  } catch (error) {
    console.error('Email conflict check error:', error);
    return {
      hasConflict: false,
    };
  }
}
