'use client';

// 文件上传测试页面
// 用于测试文件上传功能

import { useState } from 'react';
import { useAuth } from '@/lib/auth/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Loader2, Upload, File, Image, FileText, Archive } from 'lucide-react';

interface UploadResult {
  success: boolean;
  file?: {
    key: string;
    url: string;
    signedUrl: string;
    name: string;
    size: number;
    type: string;
    category: string;
  };
  error?: string;
}

export default function UploadTestPage() {
  const { user, isLoading: authLoading } = useAuth();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileType, setFileType] = useState<string>('');
  const [workspaceId, setWorkspaceId] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const [uploadConfig, setUploadConfig] = useState<any>(null);

  // 获取上传配置
  const fetchUploadConfig = async () => {
    try {
      const response = await fetch('/api/workspace/upload');
      const data = await response.json();
      if (data.success) {
        setUploadConfig(data.config);
      }
    } catch (error) {
      console.error('Failed to fetch upload config:', error);
    }
  };

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  // 处理文件上传
  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      if (fileType) formData.append('type', fileType);
      if (workspaceId) formData.append('workspaceId', workspaceId);

      const response = await fetch('/api/workspace/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      setUploadResult(result);

    } catch (error) {
      setUploadResult({
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // 获取文件图标
  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (type.includes('pdf') || type.includes('text')) return <FileText className="w-5 h-5" />;
    if (type.includes('zip') || type.includes('rar')) return <Archive className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <p className="text-center text-gray-600">请先登录以使用文件上传功能</p>
            <Button className="w-full mt-4" onClick={() => window.location.href = '/auth/login'}>
              前往登录
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">文件上传测试</h1>
          <p className="text-gray-600">测试 Cloudflare R2 文件上传功能</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 上传表单 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                文件上传
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="file">选择文件</Label>
                <Input
                  id="file"
                  type="file"
                  onChange={handleFileSelect}
                  className="mt-1"
                />
                {selectedFile && (
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg flex items-center gap-3">
                    {getFileIcon(selectedFile.type)}
                    <div className="flex-1">
                      <p className="text-sm font-medium">{selectedFile.name}</p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(selectedFile.size)} • {selectedFile.type}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="fileType">文件类型 (可选)</Label>
                <select
                  id="fileType"
                  value={fileType}
                  onChange={(e) => setFileType(e.target.value)}
                  className="w-full h-10 px-3 py-2 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">选择文件类型</option>
                  <option value="icon">图标</option>
                  <option value="screenshot">截图</option>
                  <option value="document">文档</option>
                  <option value="archive">压缩包</option>
                </select>
              </div>

              <div>
                <Label htmlFor="workspaceId">工作区 ID (可选)</Label>
                <Input
                  id="workspaceId"
                  value={workspaceId}
                  onChange={(e) => setWorkspaceId(e.target.value)}
                  placeholder="输入工作区 ID"
                />
              </div>

              <Button
                onClick={handleUpload}
                disabled={!selectedFile || isUploading}
                className="w-full"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    上传中...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    上传文件
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                onClick={fetchUploadConfig}
                className="w-full"
              >
                获取上传配置
              </Button>
            </CardContent>
          </Card>

          {/* 结果显示 */}
          <Card>
            <CardHeader>
              <CardTitle>上传结果</CardTitle>
            </CardHeader>
            <CardContent>
              {uploadResult && (
                <div className="space-y-4">
                  {uploadResult.success ? (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h3 className="font-medium text-green-800 mb-2">上传成功！</h3>
                      {uploadResult.file && (
                        <div className="space-y-2 text-sm">
                          <p><strong>文件名:</strong> {uploadResult.file.name}</p>
                          <p><strong>大小:</strong> {formatFileSize(uploadResult.file.size)}</p>
                          <p><strong>类型:</strong> {uploadResult.file.type}</p>
                          <p><strong>分类:</strong> {uploadResult.file.category}</p>
                          <p><strong>存储路径:</strong> {uploadResult.file.key}</p>
                          <div className="mt-3">
                            <a
                              href={uploadResult.file.signedUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              查看文件
                            </a>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <h3 className="font-medium text-red-800 mb-2">上传失败</h3>
                      <p className="text-sm text-red-600">{uploadResult.error}</p>
                    </div>
                  )}
                </div>
              )}

              {uploadConfig && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="font-medium text-blue-800 mb-2">上传配置</h3>
                  <div className="text-sm space-y-2">
                    <div>
                      <strong>支持的文件类型:</strong>
                      <ul className="mt-1 ml-4 list-disc">
                        {Object.entries(uploadConfig.allowedTypes).map(([category, types]) => (
                          <li key={category}>
                            <strong>{category}:</strong> {(types as string[]).join(', ')}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <strong>文件大小限制:</strong>
                      <ul className="mt-1 ml-4 list-disc">
                        {Object.entries(uploadConfig.maxSizes).map(([category, size]) => (
                          <li key={category}>
                            <strong>{category}:</strong> {formatFileSize(size as number)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
