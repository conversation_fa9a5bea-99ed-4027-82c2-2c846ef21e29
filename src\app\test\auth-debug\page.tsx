'use client';

// 认证状态调试页面
// 用于调试认证状态和会话问题

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/auth-context';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, User, Key, Clock } from 'lucide-react';

export default function AuthDebugPage() {
  const { user, session, loading } = useAuth();
  const [supabaseSession, setSupabaseSession] = useState<any>(null);
  const [supabaseUser, setSupabaseUser] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 获取 Supabase 原始会话信息
  const fetchSupabaseSession = async () => {
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      setSupabaseSession({ data: sessionData, error: sessionError });
      setSupabaseUser({ data: userData, error: userError });
    } catch (error) {
      console.error('Error fetching Supabase session:', error);
    }
  };

  // 刷新会话
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await supabase.auth.refreshSession();
      await fetchSupabaseSession();
    } catch (error) {
      console.error('Error refreshing session:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchSupabaseSession();
  }, []);

  const formatDate = (timestamp?: string | number) => {
    if (!timestamp) return 'N/A';
    return new Date(typeof timestamp === 'string' ? timestamp : timestamp * 1000).toLocaleString();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">认证状态调试</h1>
          <p className="text-gray-600">检查认证状态和会话信息</p>
          
          <div className="mt-4">
            <Button onClick={handleRefresh} disabled={isRefreshing}>
              {isRefreshing ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              刷新会话
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Auth Context 状态 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Auth Context 状态
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <strong>Loading:</strong> {loading ? 'true' : 'false'}
              </div>
              
              <div>
                <strong>User:</strong>
                {user ? (
                  <div className="mt-2 p-3 bg-green-50 rounded-lg">
                    <div><strong>ID:</strong> {user.id}</div>
                    <div><strong>Email:</strong> {user.email}</div>
                    <div><strong>Display Name:</strong> {user.displayName || 'N/A'}</div>
                    <div><strong>Metadata:</strong></div>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(user.metadata, null, 2)}
                    </pre>
                  </div>
                ) : (
                  <div className="mt-2 p-3 bg-red-50 rounded-lg text-red-700">
                    No user found
                  </div>
                )}
              </div>

              <div>
                <strong>Session:</strong>
                {session ? (
                  <div className="mt-2 p-3 bg-green-50 rounded-lg">
                    <div><strong>Access Token:</strong> {session.accessToken ? 'Present' : 'Missing'}</div>
                    <div><strong>Refresh Token:</strong> {session.refreshToken ? 'Present' : 'Missing'}</div>
                    <div><strong>Expires At:</strong> {formatDate(session.expiresAt)}</div>
                  </div>
                ) : (
                  <div className="mt-2 p-3 bg-red-50 rounded-lg text-red-700">
                    No session found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Supabase 原始状态 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                Supabase 原始状态
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <strong>Session Data:</strong>
                {supabaseSession ? (
                  <div className="mt-2">
                    {supabaseSession.error ? (
                      <div className="p-3 bg-red-50 rounded-lg text-red-700">
                        <strong>Error:</strong> {supabaseSession.error.message}
                      </div>
                    ) : supabaseSession.data.session ? (
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div><strong>User ID:</strong> {supabaseSession.data.session.user?.id}</div>
                        <div><strong>Email:</strong> {supabaseSession.data.session.user?.email}</div>
                        <div><strong>Provider:</strong> {supabaseSession.data.session.user?.app_metadata?.provider}</div>
                        <div><strong>Expires At:</strong> {formatDate(supabaseSession.data.session.expires_at)}</div>
                      </div>
                    ) : (
                      <div className="p-3 bg-yellow-50 rounded-lg text-yellow-700">
                        No session data
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    Loading...
                  </div>
                )}
              </div>

              <div>
                <strong>User Data:</strong>
                {supabaseUser ? (
                  <div className="mt-2">
                    {supabaseUser.error ? (
                      <div className="p-3 bg-red-50 rounded-lg text-red-700">
                        <strong>Error:</strong> {supabaseUser.error.message}
                      </div>
                    ) : supabaseUser.data.user ? (
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div><strong>User ID:</strong> {supabaseUser.data.user.id}</div>
                        <div><strong>Email:</strong> {supabaseUser.data.user.email}</div>
                        <div><strong>Email Confirmed:</strong> {supabaseUser.data.user.email_confirmed_at ? 'Yes' : 'No'}</div>
                        <div><strong>Created At:</strong> {formatDate(supabaseUser.data.user.created_at)}</div>
                        <div><strong>Last Sign In:</strong> {formatDate(supabaseUser.data.user.last_sign_in_at)}</div>
                      </div>
                    ) : (
                      <div className="p-3 bg-yellow-50 rounded-lg text-yellow-700">
                        No user data
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    Loading...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Cookie 信息 */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Cookie 信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div><strong>Document Cookies:</strong></div>
                <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto">
                  {document.cookie || 'No cookies found'}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 测试按钮 */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>测试操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <Button onClick={() => window.location.href = '/auth/login'}>
                  前往登录页
                </Button>
                <Button onClick={() => window.location.href = '/dashboard'}>
                  前往仪表盘
                </Button>
                <Button onClick={() => window.location.href = '/test/upload'}>
                  前往上传测试
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
