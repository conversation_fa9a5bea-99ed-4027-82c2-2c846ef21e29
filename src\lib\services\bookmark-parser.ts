// HTML 书签解析服务
// 支持解析不同浏览器导出的书签文件

import * as cheerio from 'cheerio';

// 书签数据结构
export interface ParsedBookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  tags?: string[];
  favicon?: string;
  addedDate?: Date;
  lastModified?: Date;
  folderId?: string;
  folderPath?: string[];
}

// 文件夹数据结构
export interface ParsedFolder {
  id: string;
  name: string;
  parentId?: string;
  path: string[];
  addedDate?: Date;
  lastModified?: Date;
}

// 解析结果
export interface BookmarkParseResult {
  bookmarks: ParsedBookmark[];
  folders: ParsedFolder[];
  totalBookmarks: number;
  totalFolders: number;
  browserType?: string;
  exportDate?: Date;
  errors: string[];
  warnings: string[];
}

// 浏览器类型检测
export enum BrowserType {
  CHROME = 'chrome',
  FIREFOX = 'firefox',
  SAFARI = 'safari',
  EDGE = 'edge',
  OPERA = 'opera',
  UNKNOWN = 'unknown',
}

export class BookmarkParser {
  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // 检测浏览器类型
  private detectBrowserType(html: string): BrowserType {
    const lowerHtml = html.toLowerCase();
    
    if (lowerHtml.includes('chrome') || lowerHtml.includes('chromium')) {
      return BrowserType.CHROME;
    }
    if (lowerHtml.includes('firefox') || lowerHtml.includes('mozilla')) {
      return BrowserType.FIREFOX;
    }
    if (lowerHtml.includes('safari')) {
      return BrowserType.SAFARI;
    }
    if (lowerHtml.includes('edge')) {
      return BrowserType.EDGE;
    }
    if (lowerHtml.includes('opera')) {
      return BrowserType.OPERA;
    }
    
    return BrowserType.UNKNOWN;
  }

  // 解析时间戳
  private parseTimestamp(timestamp?: string): Date | undefined {
    if (!timestamp) return undefined;
    
    try {
      // Chrome/Edge 使用 Unix 时间戳（秒）
      const num = parseInt(timestamp);
      if (num > 0) {
        // 如果是 Chrome 的微秒时间戳，需要转换
        if (num > 1000000000000) {
          return new Date(num / 1000);
        }
        return new Date(num * 1000);
      }
    } catch (error) {
      console.warn('Failed to parse timestamp:', timestamp);
    }
    
    return undefined;
  }

  // 清理和标准化 URL
  private cleanUrl(url: string): string {
    if (!url) return '';
    
    try {
      // 移除可能的前缀
      url = url.trim();
      if (url.startsWith('javascript:')) return '';
      if (url.startsWith('data:')) return '';
      
      // 确保有协议
      if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('ftp://')) {
        url = 'https://' + url;
      }
      
      // 验证 URL 格式
      new URL(url);
      return url;
    } catch (error) {
      return '';
    }
  }

  // 解析文件夹结构
  private parseFolders($: cheerio.CheerioAPI, element: cheerio.Cheerio<cheerio.Element>, parentPath: string[] = []): ParsedFolder[] {
    const folders: ParsedFolder[] = [];
    
    element.find('> dt').each((_, dt) => {
      const $dt = $(dt);
      const $h3 = $dt.find('> h3');
      
      if ($h3.length > 0) {
        const folderName = $h3.text().trim();
        if (folderName) {
          const folder: ParsedFolder = {
            id: this.generateId(),
            name: folderName,
            path: [...parentPath, folderName],
            addedDate: this.parseTimestamp($h3.attr('add_date')),
            lastModified: this.parseTimestamp($h3.attr('last_modified')),
          };
          
          folders.push(folder);
          
          // 递归解析子文件夹
          const $dl = $dt.find('> dl');
          if ($dl.length > 0) {
            const subFolders = this.parseFolders($, $dl, folder.path);
            folders.push(...subFolders);
          }
        }
      }
    });
    
    return folders;
  }

  // 解析书签
  private parseBookmarks($: cheerio.CheerioAPI, element: cheerio.Cheerio<cheerio.Element>, folderPath: string[] = []): ParsedBookmark[] {
    const bookmarks: ParsedBookmark[] = [];
    
    element.find('dt').each((_, dt) => {
      const $dt = $(dt);
      const $a = $dt.find('> a');
      
      if ($a.length > 0) {
        const title = $a.text().trim();
        const url = this.cleanUrl($a.attr('href') || '');
        
        if (title && url) {
          const bookmark: ParsedBookmark = {
            id: this.generateId(),
            title,
            url,
            folderPath: folderPath.length > 0 ? folderPath : undefined,
            addedDate: this.parseTimestamp($a.attr('add_date')),
            lastModified: this.parseTimestamp($a.attr('last_modified')),
            favicon: $a.attr('icon'),
            tags: $a.attr('tags')?.split(',').map(tag => tag.trim()).filter(Boolean),
          };
          
          // 获取描述（如果有）
          const $dd = $dt.next('dd');
          if ($dd.length > 0) {
            bookmark.description = $dd.text().trim();
          }
          
          bookmarks.push(bookmark);
        }
      }
      
      // 递归处理子文件夹中的书签
      const $dl = $dt.find('> dl');
      if ($dl.length > 0) {
        const $h3 = $dt.find('> h3');
        const folderName = $h3.text().trim();
        const newPath = folderName ? [...folderPath, folderName] : folderPath;
        const subBookmarks = this.parseBookmarks($, $dl, newPath);
        bookmarks.push(...subBookmarks);
      }
    });
    
    return bookmarks;
  }

  // 主解析方法
  async parseBookmarkFile(htmlContent: string): Promise<BookmarkParseResult> {
    const result: BookmarkParseResult = {
      bookmarks: [],
      folders: [],
      totalBookmarks: 0,
      totalFolders: 0,
      errors: [],
      warnings: [],
    };

    try {
      // 检测浏览器类型
      result.browserType = this.detectBrowserType(htmlContent);

      // 使用 Cheerio 解析 HTML
      const $ = cheerio.load(htmlContent);
      
      // 查找书签容器
      const $bookmarkContainer = $('dl').first();
      
      if ($bookmarkContainer.length === 0) {
        result.errors.push('未找到有效的书签数据结构');
        return result;
      }

      // 解析文件夹结构
      result.folders = this.parseFolders($, $bookmarkContainer);
      
      // 解析书签
      result.bookmarks = this.parseBookmarks($, $bookmarkContainer);
      
      // 统计信息
      result.totalBookmarks = result.bookmarks.length;
      result.totalFolders = result.folders.length;
      
      // 检查导出日期
      const $title = $('title');
      if ($title.length > 0) {
        const titleText = $title.text();
        const dateMatch = titleText.match(/(\d{4}-\d{2}-\d{2})/);
        if (dateMatch) {
          result.exportDate = new Date(dateMatch[1]);
        }
      }

      // 验证结果
      if (result.totalBookmarks === 0) {
        result.warnings.push('未找到任何书签');
      }

      // 检查重复的 URL
      const urlSet = new Set();
      const duplicates: string[] = [];
      result.bookmarks.forEach(bookmark => {
        if (urlSet.has(bookmark.url)) {
          duplicates.push(bookmark.url);
        } else {
          urlSet.add(bookmark.url);
        }
      });

      if (duplicates.length > 0) {
        result.warnings.push(`发现 ${duplicates.length} 个重复的书签`);
      }

    } catch (error) {
      result.errors.push(`解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    return result;
  }

  // 验证书签文件格式
  static validateBookmarkFile(htmlContent: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // 基本 HTML 结构检查
      if (!htmlContent.includes('<html') && !htmlContent.includes('<HTML')) {
        errors.push('不是有效的 HTML 文件');
      }

      // 书签特征检查
      const hasBookmarkStructure = htmlContent.includes('<DL>') || htmlContent.includes('<dl>');
      const hasBookmarkLinks = htmlContent.includes('<A HREF') || htmlContent.includes('<a href');
      
      if (!hasBookmarkStructure) {
        errors.push('未找到书签文件夹结构');
      }
      
      if (!hasBookmarkLinks) {
        errors.push('未找到书签链接');
      }

      // 文件大小检查（字符串长度作为近似）
      if (htmlContent.length > 50 * 1024 * 1024) { // 50MB
        errors.push('文件过大，可能不是标准的书签文件');
      }

    } catch (error) {
      errors.push('文件格式验证失败');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
