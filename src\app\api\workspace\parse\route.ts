// 书签解析 API 端点
// 处理上传的书签文件解析

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { BookmarkParser } from '@/lib/services/bookmark-parser';
import { storageProvider } from '@/lib/services/storage-service';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { fileKey, workspaceId } = body;

    if (!fileKey) {
      return NextResponse.json({
        success: false,
        error: 'File key is required',
      }, { status: 400 });
    }

    // 验证文件所有权
    if (!fileKey.includes(user.id)) {
      return NextResponse.json({
        success: false,
        error: 'Access denied',
      }, { status: 403 });
    }

    // 从存储中下载文件
    const downloadResult = await storageProvider.download(fileKey);
    
    if (downloadResult.error || !downloadResult.data) {
      return NextResponse.json({
        success: false,
        error: 'Failed to download file',
        details: downloadResult.error?.message,
      }, { status: 500 });
    }

    // 读取文件内容
    const htmlContent = await downloadResult.data.text();

    // 验证文件格式
    const validation = BookmarkParser.validateBookmarkFile(htmlContent);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Invalid bookmark file format',
        details: validation.errors,
      }, { status: 400 });
    }

    // 解析书签文件
    const parser = new BookmarkParser();
    const parseResult = await parser.parseBookmarkFile(htmlContent);

    if (parseResult.errors.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Failed to parse bookmark file',
        details: parseResult.errors,
        warnings: parseResult.warnings,
      }, { status: 400 });
    }

    // 返回解析结果
    return NextResponse.json({
      success: true,
      message: 'Bookmark file parsed successfully',
      data: {
        totalBookmarks: parseResult.totalBookmarks,
        totalFolders: parseResult.totalFolders,
        browserType: parseResult.browserType,
        exportDate: parseResult.exportDate,
        bookmarks: parseResult.bookmarks,
        folders: parseResult.folders,
        warnings: parseResult.warnings,
      },
      metadata: {
        fileKey,
        workspaceId,
        userId: user.id,
        parsedAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Bookmark parse API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// 获取解析状态或历史
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
      }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const workspaceId = searchParams.get('workspaceId');

    // 这里可以返回解析历史或状态信息
    // 目前返回基本信息
    return NextResponse.json({
      success: true,
      data: {
        supportedFormats: ['HTML', 'Netscape Bookmark Format'],
        maxFileSize: '10MB',
        supportedBrowsers: ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera'],
        features: [
          'Folder structure preservation',
          'Bookmark metadata extraction',
          'Duplicate detection',
          'Browser type detection',
          'Export date detection',
        ],
      },
    });

  } catch (error) {
    console.error('Parse info API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
    }, { status: 500 });
  }
}
