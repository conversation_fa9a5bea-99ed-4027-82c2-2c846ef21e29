import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';

/**
 * 确保用户档案存在的API端点
 * 在用户登录后调用，确保数据库中有用户档案
 */
export async function POST(request: NextRequest) {
  try {
    const { userId, email, displayName } = await request.json();

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 创建Supabase客户端
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            // 在API路由中不能设置cookies
          },
          remove(name: string, options: CookieOptions) {
            // 在API路由中不能删除cookies
          },
        },
      }
    );

    // 验证用户身份
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user || user.id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 使用service role key来绕过RLS
    const supabaseAdmin = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        cookies: {
          get() { return undefined; },
          set() {},
          remove() {},
        },
      }
    );

    // 尝试插入用户档案
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .upsert({
        id: userId,
        email: email.toLowerCase(),
        display_name: displayName || email.split('@')[0],
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (profileError) {
      console.error('Profile creation error:', profileError);
      return NextResponse.json(
        { error: 'Failed to create profile', details: profileError.message },
        { status: 500 }
      );
    }

    // 创建用户工作空间（如果不存在）
    const { error: workspaceError } = await supabaseAdmin
      .from('user_bookmark_workspace')
      .upsert({
        user_id: userId,
        updated_at: new Date().toISOString(),
      });

    if (workspaceError && !workspaceError.message.includes('duplicate key')) {
      console.error('Workspace creation error:', workspaceError);
    }

    // 创建默认分类（如果不存在）
    const { error: categoriesError } = await supabaseAdmin
      .from('user_categories')
      .upsert([
        {
          user_id: userId,
          name: '未分类',
          type: 'manual',
          color: '#6b7280',
          is_default: true,
          sort_order: 0,
        },
        {
          user_id: userId,
          name: '收件箱',
          type: 'manual',
          color: '#3b82f6',
          is_default: true,
          sort_order: 1,
        },
      ]);

    if (categoriesError && !categoriesError.message.includes('duplicate key')) {
      console.error('Categories creation error:', categoriesError);
    }

    return NextResponse.json({
      success: true,
      profile,
    });

  } catch (error) {
    console.error('Ensure profile error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
